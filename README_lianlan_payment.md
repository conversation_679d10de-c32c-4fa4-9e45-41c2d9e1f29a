# Lianlan支付平台API集成

这是一个用于集成Lianlan支付平台API的Python客户端，专门用于采购甲方向供应商发起付款。

## 文件结构

```
├── cjc_http_client.py          # HTTP客户端（已存在）
├── lianlan_payment_client.py   # Lianlan支付API客户端
├── config.py                   # 配置文件
├── payment_example.py          # 使用示例
└── README_lianlan_payment.md   # 说明文档
```

## 功能特性

- ✅ 创建未分配账单
- ✅ 查询账单状态
- ✅ 取消账单
- ✅ 获取支持的付款方式
- ✅ 批量创建账单
- ✅ 自动签名验证
- ✅ 错误处理和重试机制

## 快速开始

### 1. 配置API密钥

编辑 `config.py` 文件，填入您的API密钥：

```python
LIANLAN_CONFIG = {
    'API_KEY': 'your_actual_api_key',
    'SECRET_KEY': 'your_actual_secret_key',
    'BASE_URL': 'https://api.lianlianglobal.com',
    'ENVIRONMENT': 'sandbox'  # 测试环境，生产环境改为'production'
}
```

### 2. 基本使用示例

```python
from lianlan_payment_client import LianlanPaymentClient
from config import get_config

# 获取配置
config = get_config('sandbox')

# 创建客户端
with LianlanPaymentClient(
    api_key=config['API_KEY'],
    secret_key=config['SECRET_KEY'],
    base_url=config['BASE_URL']
) as client:
    
    # 供应商信息
    supplier_info = {
        'name': '供应商名称',
        'email': '<EMAIL>',
        'phone': '+86-138-0000-0000',
        'address': {
            'country': 'CN',
            'state': '广东省',
            'city': '深圳市',
            'street': '南山区科技园',
            'postal_code': '518000'
        },
        'bank_info': {
            'bank_name': '中国银行',
            'account_number': '**********',
            'swift_code': 'BKCHCNBJ',
            'account_holder': '供应商名称'
        }
    }
    
    # 创建未分配账单
    response = client.create_unassigned_bill(
        amount=1000.00,
        currency='USD',
        supplier_info=supplier_info,
        description='采购货款 - 订单号: PO202312001',
        reference_id='REF_PO202312001'
    )
    
    print("账单创建结果:", response)
```

### 3. 运行示例

```bash
python payment_example.py
```

## API方法说明

### create_unassigned_bill()

创建未分配账单

**参数:**
- `amount` (float): 付款金额
- `currency` (str): 货币代码 (USD, EUR, CNY等)
- `supplier_info` (dict): 供应商信息
- `description` (str): 付款描述
- `reference_id` (str, 可选): 参考ID

**返回:** API响应字典

### query_bill_status()

查询账单状态

**参数:**
- `bill_id` (str): 账单ID

**返回:** 账单状态信息

### cancel_bill()

取消账单

**参数:**
- `bill_id` (str): 账单ID
- `reason` (str, 可选): 取消原因

**返回:** 取消结果

### get_payment_methods()

获取支持的付款方式

**参数:**
- `currency` (str, 可选): 货币代码

**返回:** 支付方式列表

## 供应商信息格式

```python
supplier_info = {
    'name': '供应商名称',
    'email': '<EMAIL>',
    'phone': '+86-138-0000-0000',
    'address': {
        'country': 'CN',           # 国家代码
        'state': '广东省',         # 省/州
        'city': '深圳市',          # 城市
        'street': '南山区科技园',   # 街道地址
        'postal_code': '518000'    # 邮政编码
    },
    'bank_info': {
        'bank_name': '中国银行',           # 银行名称
        'account_number': '**********',   # 账户号码
        'swift_code': 'BKCHCNBJ',        # SWIFT代码
        'account_holder': '供应商名称'     # 账户持有人
    },
    'business_info': {
        'registration_number': '91440300000000000X',  # 注册号
        'tax_id': '91440300000000000X',              # 税号
        'business_type': 'company'                   # 企业类型
    }
}
```

## 支持的货币

- USD (美元)
- EUR (欧元)
- GBP (英镑)
- JPY (日元)
- CNY (人民币)
- HKD (港币)
- SGD (新加坡元)
- AUD (澳元)
- CAD (加元)

## 错误处理

所有API调用都包含错误处理，会抛出详细的异常信息：

```python
try:
    response = client.create_unassigned_bill(...)
except Exception as e:
    print(f"创建账单失败: {str(e)}")
```

## 注意事项

1. **API密钥安全**: 请妥善保管您的API密钥，不要提交到版本控制系统
2. **环境配置**: 测试时使用沙盒环境，生产时切换到生产环境
3. **签名验证**: 客户端会自动处理API签名，无需手动处理
4. **错误重试**: 建议在生产环境中实现重试机制
5. **日志记录**: 建议添加详细的日志记录用于问题排查

## 生产环境部署

1. 修改 `config.py` 中的环境配置：
```python
'ENVIRONMENT': 'production'
```

2. 配置生产环境的API密钥：
```python
PRODUCTION_CONFIG = {
    'API_KEY': 'your_production_api_key',
    'SECRET_KEY': 'your_production_secret_key',
    'BASE_URL': 'https://api.lianlianglobal.com'
}
```

3. 添加日志和监控
4. 实现错误重试机制
5. 设置适当的超时时间

## 技术支持

如果遇到问题，请检查：
1. API密钥是否正确配置
2. 网络连接是否正常
3. 请求参数是否符合API规范
4. 查看详细的错误信息进行排查

更多信息请参考Lianlan官方API文档：https://developer.lianlianglobal.com/
