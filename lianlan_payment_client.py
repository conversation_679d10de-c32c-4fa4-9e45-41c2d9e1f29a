"""
Lianlan支付平台API客户端
用于创建未分配账单和处理付款
"""

import hashlib
import hmac
import json
import time
from typing import Dict, Any, Optional
from cjc_http_client import HttpClient


class LianlanPaymentClient:
    """Lianlan支付平台API客户端"""
    
    def __init__(self, api_key: str, secret_key: str, base_url: str = "https://api.lianlianglobal.com"):
        """
        初始化Lianlan支付客户端
        
        Args:
            api_key: API密钥
            secret_key: 密钥用于签名
            base_url: API基础URL
        """
        self.api_key = api_key
        self.secret_key = secret_key
        self.base_url = base_url.rstrip('/')
        self.http_client = HttpClient(base_url=self.base_url)
    
    def _generate_signature(self, params: Dict[str, Any], timestamp: str) -> str:
        """
        生成API签名
        
        Args:
            params: 请求参数
            timestamp: 时间戳
            
        Returns:
            签名字符串
        """
        # 将参数按键名排序
        sorted_params = sorted(params.items())
        
        # 构建签名字符串
        sign_string = ""
        for key, value in sorted_params:
            if value is not None and value != "":
                sign_string += f"{key}={value}&"
        
        # 添加时间戳和密钥
        sign_string += f"timestamp={timestamp}&key={self.secret_key}"
        
        # 生成MD5签名
        signature = hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
        return signature
    
    def _get_headers(self, params: Dict[str, Any]) -> Dict[str, str]:
        """
        获取请求头
        
        Args:
            params: 请求参数
            
        Returns:
            请求头字典
        """
        timestamp = str(int(time.time()))
        signature = self._generate_signature(params, timestamp)
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}',
            'X-Timestamp': timestamp,
            'X-Signature': signature,
            'Accept': 'application/json'
        }
        
        return headers
    
    def create_unassigned_bill(self, 
                              amount: float,
                              currency: str,
                              supplier_info: Dict[str, Any],
                              description: str = "",
                              reference_id: Optional[str] = None) -> Dict[str, Any]:
        """
        创建未分配账单
        
        Args:
            amount: 付款金额
            currency: 货币代码 (如: USD, CNY, EUR)
            supplier_info: 供应商信息
            description: 付款描述
            reference_id: 参考ID
            
        Returns:
            API响应结果
        """
        # 构建请求参数
        params = {
            'amount': amount,
            'currency': currency,
            'supplier_info': supplier_info,
            'description': description,
            'reference_id': reference_id or f"REF_{int(time.time())}",
            'timestamp': int(time.time())
        }
        
        # 移除空值
        params = {k: v for k, v in params.items() if v is not None}
        
        # 获取请求头
        headers = self._get_headers(params)
        
        try:
            # 发送POST请求创建未分配账单
            response = self.http_client.post(
                endpoint='/api/v1/bills/unassigned',
                json_data=params,
                headers=headers
            )
            
            return response
            
        except Exception as e:
            raise Exception(f"创建未分配账单失败: {str(e)}")
    
    def query_bill_status(self, bill_id: str) -> Dict[str, Any]:
        """
        查询账单状态
        
        Args:
            bill_id: 账单ID
            
        Returns:
            账单状态信息
        """
        params = {
            'bill_id': bill_id,
            'timestamp': int(time.time())
        }
        
        headers = self._get_headers(params)
        
        try:
            response = self.http_client.get(
                endpoint=f'/api/v1/bills/{bill_id}',
                headers=headers
            )
            
            return response
            
        except Exception as e:
            raise Exception(f"查询账单状态失败: {str(e)}")
    
    def cancel_bill(self, bill_id: str, reason: str = "") -> Dict[str, Any]:
        """
        取消账单
        
        Args:
            bill_id: 账单ID
            reason: 取消原因
            
        Returns:
            取消结果
        """
        params = {
            'bill_id': bill_id,
            'reason': reason,
            'timestamp': int(time.time())
        }
        
        headers = self._get_headers(params)
        
        try:
            response = self.http_client.post(
                endpoint=f'/api/v1/bills/{bill_id}/cancel',
                json_data=params,
                headers=headers
            )
            
            return response
            
        except Exception as e:
            raise Exception(f"取消账单失败: {str(e)}")
    
    def get_payment_methods(self, currency: str = "") -> Dict[str, Any]:
        """
        获取支持的付款方式
        
        Args:
            currency: 货币代码（可选）
            
        Returns:
            支付方式列表
        """
        params = {
            'currency': currency,
            'timestamp': int(time.time())
        } if currency else {'timestamp': int(time.time())}
        
        headers = self._get_headers(params)
        
        try:
            response = self.http_client.get(
                endpoint='/api/v1/payment-methods',
                params=params,
                headers=headers
            )
            
            return response
            
        except Exception as e:
            raise Exception(f"获取付款方式失败: {str(e)}")
    
    def close(self):
        """关闭HTTP客户端连接"""
        if self.http_client:
            self.http_client.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
