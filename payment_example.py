"""
Lianlan支付平台使用示例
演示如何使用LianlanPaymentClient创建未分配账单和处理付款
"""

import json
from lianlan_payment_client import LianlanPaymentClient
from config import get_config, SUPPLIER_INFO_TEMPLATE, SUPPORTED_CURRENCIES


def create_supplier_info():
    """
    创建供应商信息
    
    Returns:
        供应商信息字典
    """
    supplier_info = {
        'name': '深圳科技有限公司',
        'email': '<EMAIL>',
        'phone': '+86-138-0000-0000',
        'address': {
            'country': 'CN',
            'state': '广东省',
            'city': '深圳市',
            'street': '南山区科技园南区R2-A栋',
            'postal_code': '518057'
        },
        'bank_info': {
            'bank_name': '中国银行深圳分行',
            'account_number': '****************',
            'swift_code': 'BKCHCNBJ300',
            'account_holder': '深圳科技有限公司'
        },
        'business_info': {
            'registration_number': '91440300MA5DC123XX',
            'tax_id': '91440300MA5DC123XX',
            'business_type': 'company'
        }
    }
    return supplier_info


def example_create_unassigned_bill():
    """
    示例：创建未分配账单
    """
    print("=== 创建未分配账单示例 ===")
    
    # 获取配置
    config = get_config('sandbox')  # 使用沙盒环境
    
    # 创建支付客户端
    with LianlanPaymentClient(
        api_key=config['API_KEY'],
        secret_key=config['SECRET_KEY'],
        base_url=config['BASE_URL']
    ) as client:
        
        try:
            # 准备供应商信息
            supplier_info = create_supplier_info()
            
            # 创建未分配账单
            response = client.create_unassigned_bill(
                amount=1000.00,
                currency='USD',
                supplier_info=supplier_info,
                description='采购货款 - 订单号: PO202312001',
                reference_id='REF_PO202312001'
            )
            
            print("✅ 账单创建成功:")
            print(json.dumps(response, indent=2, ensure_ascii=False))
            
            # 如果创建成功，返回账单ID用于后续操作
            if 'bill_id' in response:
                return response['bill_id']
            
        except Exception as e:
            print(f"❌ 创建账单失败: {str(e)}")
            return None


def example_query_bill_status(bill_id: str):
    """
    示例：查询账单状态
    
    Args:
        bill_id: 账单ID
    """
    print(f"\n=== 查询账单状态示例 (ID: {bill_id}) ===")
    
    config = get_config('sandbox')
    
    with LianlanPaymentClient(
        api_key=config['API_KEY'],
        secret_key=config['SECRET_KEY'],
        base_url=config['BASE_URL']
    ) as client:
        
        try:
            response = client.query_bill_status(bill_id)
            print("✅ 账单状态查询成功:")
            print(json.dumps(response, indent=2, ensure_ascii=False))
            
        except Exception as e:
            print(f"❌ 查询账单状态失败: {str(e)}")


def example_get_payment_methods():
    """
    示例：获取支持的付款方式
    """
    print("\n=== 获取支付方式示例 ===")
    
    config = get_config('sandbox')
    
    with LianlanPaymentClient(
        api_key=config['API_KEY'],
        secret_key=config['SECRET_KEY'],
        base_url=config['BASE_URL']
    ) as client:
        
        try:
            # 获取USD的支付方式
            response = client.get_payment_methods('USD')
            print("✅ 支付方式获取成功:")
            print(json.dumps(response, indent=2, ensure_ascii=False))
            
        except Exception as e:
            print(f"❌ 获取支付方式失败: {str(e)}")


def example_cancel_bill(bill_id: str):
    """
    示例：取消账单
    
    Args:
        bill_id: 账单ID
    """
    print(f"\n=== 取消账单示例 (ID: {bill_id}) ===")
    
    config = get_config('sandbox')
    
    with LianlanPaymentClient(
        api_key=config['API_KEY'],
        secret_key=config['SECRET_KEY'],
        base_url=config['BASE_URL']
    ) as client:
        
        try:
            response = client.cancel_bill(
                bill_id=bill_id,
                reason='采购订单已取消'
            )
            print("✅ 账单取消成功:")
            print(json.dumps(response, indent=2, ensure_ascii=False))
            
        except Exception as e:
            print(f"❌ 取消账单失败: {str(e)}")


def batch_create_bills():
    """
    示例：批量创建账单
    """
    print("\n=== 批量创建账单示例 ===")
    
    config = get_config('sandbox')
    
    # 批量账单数据
    bills_data = [
        {
            'amount': 500.00,
            'currency': 'USD',
            'description': '采购货款 - 订单号: PO202312002',
            'reference_id': 'REF_PO202312002'
        },
        {
            'amount': 750.00,
            'currency': 'EUR',
            'description': '采购货款 - 订单号: PO202312003',
            'reference_id': 'REF_PO202312003'
        },
        {
            'amount': 300.00,
            'currency': 'GBP',
            'description': '采购货款 - 订单号: PO202312004',
            'reference_id': 'REF_PO202312004'
        }
    ]
    
    with LianlanPaymentClient(
        api_key=config['API_KEY'],
        secret_key=config['SECRET_KEY'],
        base_url=config['BASE_URL']
    ) as client:
        
        supplier_info = create_supplier_info()
        created_bills = []
        
        for i, bill_data in enumerate(bills_data, 1):
            try:
                print(f"创建第 {i} 个账单...")
                
                response = client.create_unassigned_bill(
                    amount=bill_data['amount'],
                    currency=bill_data['currency'],
                    supplier_info=supplier_info,
                    description=bill_data['description'],
                    reference_id=bill_data['reference_id']
                )
                
                created_bills.append(response)
                print(f"✅ 第 {i} 个账单创建成功")
                
            except Exception as e:
                print(f"❌ 第 {i} 个账单创建失败: {str(e)}")
        
        print(f"\n批量创建完成，成功创建 {len(created_bills)} 个账单")
        return created_bills


def main():
    """
    主函数 - 运行所有示例
    """
    print("🚀 Lianlan支付平台API使用示例")
    print("=" * 50)
    
    # 1. 获取支付方式
    example_get_payment_methods()
    
    # 2. 创建未分配账单
    bill_id = example_create_unassigned_bill()
    
    if bill_id:
        # 3. 查询账单状态
        example_query_bill_status(bill_id)
        
        # 4. 取消账单（可选）
        # example_cancel_bill(bill_id)
    
    # 5. 批量创建账单
    batch_create_bills()
    
    print("\n✨ 所有示例执行完成")


if __name__ == "__main__":
    # 在运行前请确保在config.py中配置了正确的API密钥
    print("⚠️  请确保在config.py中配置了正确的API密钥和密钥")
    print("⚠️  当前使用的是沙盒环境，生产环境请修改配置")
    print()
    
    main()
