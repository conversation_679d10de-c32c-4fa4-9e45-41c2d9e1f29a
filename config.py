"""
Lianlan支付平台配置文件
请根据实际情况修改配置信息
"""

# Lianlan支付平台API配置
LIANLAN_CONFIG = {
    # API密钥 - 从Lianlan开发者后台获取
    'API_KEY': 'your_api_key_here',
    
    # 密钥 - 用于签名验证
    'SECRET_KEY': 'your_secret_key_here',
    
    # API基础URL
    'BASE_URL': 'https://api.lianlianglobal.com',
    
    # 请求超时时间（秒）
    'TIMEOUT': 30,
    
    # 默认货币
    'DEFAULT_CURRENCY': 'USD',
    
    # 环境配置 (sandbox/production)
    'ENVIRONMENT': 'sandbox'  # 测试环境使用sandbox，生产环境使用production
}

# 沙盒环境配置
SANDBOX_CONFIG = {
    'API_KEY': 'sandbox_api_key',
    'SECRET_KEY': 'sandbox_secret_key',
    'BASE_URL': 'https://sandbox-api.lianlianglobal.com'
}

# 生产环境配置
PRODUCTION_CONFIG = {
    'API_KEY': 'production_api_key',
    'SECRET_KEY': 'production_secret_key',
    'BASE_URL': 'https://api.lianlianglobal.com'
}

def get_config(environment: str = None):
    """
    根据环境获取配置
    
    Args:
        environment: 环境名称 ('sandbox' 或 'production')
        
    Returns:
        配置字典
    """
    if environment is None:
        environment = LIANLAN_CONFIG['ENVIRONMENT']
    
    if environment == 'sandbox':
        return {**LIANLAN_CONFIG, **SANDBOX_CONFIG}
    elif environment == 'production':
        return {**LIANLAN_CONFIG, **PRODUCTION_CONFIG}
    else:
        return LIANLAN_CONFIG

# 供应商信息模板
SUPPLIER_INFO_TEMPLATE = {
    'name': '供应商名称',
    'email': '<EMAIL>',
    'phone': '+86-138-0000-0000',
    'address': {
        'country': 'CN',
        'state': '广东省',
        'city': '深圳市',
        'street': '南山区科技园',
        'postal_code': '518000'
    },
    'bank_info': {
        'bank_name': '中国银行',
        'account_number': '**********',
        'swift_code': 'BKCHCNBJ',
        'account_holder': '供应商名称'
    },
    'business_info': {
        'registration_number': '91440300000000000X',
        'tax_id': '91440300000000000X',
        'business_type': 'company'
    }
}

# 常用货币代码
SUPPORTED_CURRENCIES = [
    'USD',  # 美元
    'EUR',  # 欧元
    'GBP',  # 英镑
    'JPY',  # 日元
    'CNY',  # 人民币
    'HKD',  # 港币
    'SGD',  # 新加坡元
    'AUD',  # 澳元
    'CAD',  # 加元
]

# 付款状态常量
PAYMENT_STATUS = {
    'PENDING': 'pending',           # 待处理
    'PROCESSING': 'processing',     # 处理中
    'COMPLETED': 'completed',       # 已完成
    'FAILED': 'failed',            # 失败
    'CANCELLED': 'cancelled',       # 已取消
    'REFUNDED': 'refunded'         # 已退款
}
