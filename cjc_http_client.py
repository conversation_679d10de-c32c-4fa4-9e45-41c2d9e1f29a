import requests
from typing import Dict, Any, Optional
from requests.exceptions import RequestException
import json

class HttpClient:
    def __init__(self, base_url: str = "", timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def close(self):
        """Safely close the session."""
        if self.session:
            self.session.close()
            self.session = None # Optional: Indicate session is closed

    def _make_url(self, endpoint: str) -> str:
        endpoint = endpoint.lstrip('/')
        return f"{self.base_url}/{endpoint}" if self.base_url else endpoint

    def _handle_response(self, response: requests.Response) -> Dict:
        try:
            response.raise_for_status()
            return response.json()
        except ValueError:
            return {"status": response.status_code, "text": response.text}
        except RequestException as e:
            print(json.dumps({"status": response.status_code, "text": response.text},ensure_ascii=False))
            raise Exception(f"请求错误: {str(e)}")

    def get(self, endpoint: str, params: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict:
        try:
            response = self.session.get(
                self._make_url(endpoint),
                params=params,
                headers=headers,
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"GET请求失败: {str(e)}")

    def post(self, endpoint: str, data: Any = None, json_data: Any = None, 
             params: Optional[Dict] = None,
             headers: Optional[Dict] = None) -> Dict:
        try:
            response = self.session.post(
                self._make_url(endpoint),
                data=data,
                params=params,
                json=json_data,
                headers=headers,
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"POST请求失败: {str(e)}")

    def put(self, endpoint: str, data: Any = None, json_data: Any = None, 
            params: Optional[Dict] = None,
            headers: Optional[Dict] = None) -> Dict:
        try:
            response = self.session.put(
                self._make_url(endpoint),
                data=data,
                params=params,
                json=json_data,
                headers=headers,
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"PUT请求失败: {str(e)}")

    def delete(self, endpoint: str, params: Optional[Dict] = None, 
               headers: Optional[Dict] = None) -> Dict:
        try:
            response = self.session.delete(
                self._make_url(endpoint),
                params=params,
                headers=headers,
                timeout=self.timeout
            )
            return self._handle_response(response)
        except Exception as e:
            raise Exception(f"DELETE请求失败: {str(e)}")

    def post_file(self, endpoint: str, files: Dict, data: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict:
        """
        使用 session 发送文件上传请求
        
        :param endpoint: 请求的端点
        :param files: 文件数据，格式如：{'media': ('filename', file_object, 'content_type')}
        :param data: 附加的表单数据（可选）
        :param headers: 请求头（可选）
        :return: 响应JSON
        
        使用示例：
            with open('file.csv', 'rb') as f:
                files = {'media': ('file.csv', f, 'text/csv')}
                response = client.post_file('/upload', files=files) # Use endpoint
        """
        try:
            # 使用 session 和 _make_url
            response = self.session.post(
                self._make_url(endpoint),
                files=files,
                data=data,
                headers=headers,
                timeout=self.timeout # 使用实例的 timeout
            )
            # 复用或实现类似的响应处理
            return self._handle_response(response)
        except Exception as e:
             # 提供更具体的错误信息
            raise Exception(f"文件上传 POST 请求失败到 {endpoint}: {str(e)}")
        except json.JSONDecodeError as e:
            raise Exception(f"响应解析失败: {str(e)}")
    
    # Remove or comment out the __del__ method if __exit__ is implemented
    # def __del__(self):
    #     # This is unreliable, use __exit__ instead
    #     if hasattr(self, 'session') and self.session:
    #          self.session.close()